import { APP_CONSTANTS, NODE_ENVIRONMENTS } from '../common/constants';
import { AppConfig } from '../common/types';

export default (): AppConfig => ({
  port:
    parseInt(process.env.PORT ?? String(APP_CONSTANTS.DEFAULT_PORT), 10) ||
    APP_CONSTANTS.DEFAULT_PORT,
  nodeEnv: process.env.NODE_ENV ?? NODE_ENVIRONMENTS.DEVELOPMENT,
  openai: {
    apiKey: process.env.OPENAI_API_KEY ?? '',
    baseUrl:
      process.env.OPENAI_BASE_URL ?? APP_CONSTANTS.DEFAULT_OPENAI_BASE_URL,
  },
  cors: {
    origin: process.env.CORS_ORIGIN ?? '*',
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },
  throttle: {
    ttl:
      parseInt(
        process.env.THROTTLE_TTL ?? String(APP_CONSTANTS.DEFAULT_THROTTLE_TTL),
        10,
      ) || APP_CONSTANTS.DEFAULT_THROTTLE_TTL,
    limit:
      parseInt(
        process.env.THROTTLE_LIMIT ??
          String(APP_CONSTANTS.DEFAULT_THROTTLE_LIMIT),
        10,
      ) || APP_CONSTANTS.DEFAULT_THROTTLE_LIMIT,
  },
  logging: {
    level: process.env.LOG_LEVEL ?? APP_CONSTANTS.DEFAULT_LOG_LEVEL,
    format: process.env.LOG_FORMAT ?? APP_CONSTANTS.DEFAULT_LOG_FORMAT,
  },
});
