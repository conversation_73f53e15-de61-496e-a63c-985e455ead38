import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { LoggerModule } from './common/logger/logger.module';
import { HealthModule } from './health/health.module';
import { OpenAiProxyModule } from './openai-proxy/openai-proxy.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env'],
    }),
    LoggerModule,
    HealthModule,
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => [
        {
          ttl: (configService.get<number>('throttle.ttl') ?? 60) * 1000, // Convert to milliseconds
          limit: configService.get<number>('throttle.limit') ?? 10,
        },
      ],
    }),
    OpenAiProxyModule,
  ],
})
export class AppModule {}
