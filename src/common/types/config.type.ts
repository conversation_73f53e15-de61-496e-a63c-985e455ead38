export interface AppConfig {
  port: number;
  nodeEnv: string;
  openai: OpenAiConfig;
  cors: CorsConfig;
  throttle: ThrottleConfig;
  logging: LoggingConfig;
}

export interface OpenAiConfig {
  apiKey: string;
  baseUrl: string;
}

export interface CorsConfig {
  origin: string;
  credentials: boolean;
}

export interface ThrottleConfig {
  ttl: number;
  limit: number;
}

export interface LoggingConfig {
  level: string;
  format: string;
}
