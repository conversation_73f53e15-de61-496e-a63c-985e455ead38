import { Injectable, LoggerService, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CustomLoggerService implements LoggerService {
  private currentLogLevel: string;
  private logFormat: string;

  constructor(private configService: ConfigService) {
    this.currentLogLevel =
      this.configService.get<string>('logging.level') ?? 'info';
    this.logFormat = this.configService.get<string>('logging.format') ?? 'json';
  }

  private shouldLog(level: LogLevel): boolean {
    const levelMap: Record<string, number> = {
      error: 0,
      warn: 1,
      log: 2, // 'info' maps to 'log' in NestJS
      info: 2, // alias for log
      debug: 3,
      verbose: 4,
    };

    const currentLevelIndex = levelMap[this.currentLogLevel] ?? levelMap['log']!;
    const messageLevelIndex = levelMap[level] ?? levelMap['log']!;

    return messageLevelIndex <= currentLevelIndex;
  }

  private formatMessage(
    level: LogLevel,
    message: any,
    context?: string,
    trace?: string,
  ): string {
    const timestamp = new Date().toISOString();
    const pid = process.pid;

    if (this.logFormat === 'json') {
      const logObject = {
        timestamp,
        level,
        context: context || 'Application',
        message:
          typeof message === 'object' ? JSON.stringify(message) : message,
        pid,
        ...(trace && { trace }),
      };
      return JSON.stringify(logObject);
    } else {
      // Simple format
      const contextStr = context ? `[${context}] ` : '';
      return `${timestamp} [${level.toUpperCase()}] ${contextStr}${message}${trace ? `\n${trace}` : ''}`;
    }
  }

  log(message: any, context?: string) {
    if (this.shouldLog('log')) {
      console.log(this.formatMessage('log', message, context));
    }
  }

  error(message: any, trace?: string, context?: string) {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('error', message, context, trace));
    }
  }

  warn(message: any, context?: string) {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, context));
    }
  }

  debug(message: any, context?: string) {
    if (this.shouldLog('debug')) {
      console.debug(this.formatMessage('debug', message, context));
    }
  }

  verbose(message: any, context?: string) {
    if (this.shouldLog('verbose')) {
      console.log(this.formatMessage('verbose', message, context));
    }
  }
}
