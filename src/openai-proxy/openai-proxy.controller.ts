import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { OpenAiProxyService } from './openai-proxy.service';
import { OpenAiRequestDto } from './dto/openai-request.dto';
import { OpenAiResponseDto } from './dto/openai-response.dto';

@ApiTags('OpenAI Proxy')
@Controller('proxy/openai')
@UseGuards(ThrottlerGuard)
export class OpenAiProxyController {
  constructor(private readonly proxyService: OpenAiProxyService) {}

  @Post()
  @ApiOperation({
    summary: 'Proxy request to OpenAI Chat Completions API',
    description:
      'Forwards chat completion requests to OpenAI API with rate limiting and validation',
  })
  @ApiBody({
    type: OpenAiRequestDto,
    description: 'OpenAI chat completion request payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Successful response from OpenAI API',
    type: OpenAiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async handle(@Body() payload: OpenAiRequestDto): Promise<OpenAiResponseDto> {
    return this.proxyService.proxyToOpenAI(payload);
  }
}
