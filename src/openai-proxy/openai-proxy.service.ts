import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class OpenAiProxyService {
  constructor(private readonly httpService: HttpService) {}

  async proxyToOpenAI(payload: any) {
    const res = await firstValueFrom(
      this.httpService.post(
        'https://api.openai.com/v1/chat/completions',
        payload,
        {
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      ),
    );

    return res.data;
  }
}
