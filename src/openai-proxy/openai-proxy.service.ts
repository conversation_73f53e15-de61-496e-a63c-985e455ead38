import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import type { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { CustomLoggerService } from '../common/logger/logger.service';
import {
  ExternalServiceException,
  ConfigurationException,
} from '../common/exceptions';
import { OpenAiConfig } from '../common/types';
import { OpenAiRequestDto } from './dto/openai-request.dto';
import { OpenAiResponseDto } from './dto/openai-response.dto';
import { OpenAiProxyServiceInterface } from './interfaces/openai-proxy.interface';

@Injectable()
export class OpenAiProxyService implements OpenAiProxyServiceInterface {
  private readonly openai: OpenAI;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {
    const openaiConfig = this.configService.get<OpenAiConfig>('openai');

    if (!openaiConfig?.apiKey) {
      throw new ConfigurationException('OpenAI API key is not configured');
    }

    this.openai = new OpenAI({
      apiKey: openaiConfig.apiKey,
      baseURL: openaiConfig.baseUrl,
    });
  }

  async proxyToOpenAI(payload: OpenAiRequestDto): Promise<OpenAiResponseDto> {
    this.logger.log('Proxying request to OpenAI', 'OpenAiProxyService');

    try {
      // Build the request parameters, only including defined values
      const requestParams: OpenAI.Chat.Completions.ChatCompletionCreateParams =
        {
          model: payload.model,
          messages: payload.messages as ChatCompletionMessageParam[],
          stream: false, // We don't support streaming in this proxy
        };

      // Add optional parameters only if they are defined
      if (payload.temperature !== undefined) {
        requestParams.temperature = payload.temperature;
      }
      if (payload.max_tokens !== undefined) {
        // Using max_tokens for backward compatibility
        requestParams.max_tokens = payload.max_tokens;
      }
      if (payload.top_p !== undefined) {
        requestParams.top_p = payload.top_p;
      }
      if (payload.frequency_penalty !== undefined) {
        requestParams.frequency_penalty = payload.frequency_penalty;
      }
      if (payload.presence_penalty !== undefined) {
        requestParams.presence_penalty = payload.presence_penalty;
      }
      if (payload.stop !== undefined) {
        requestParams.stop = payload.stop;
      }

      const completion =
        await this.openai.chat.completions.create(requestParams);

      this.logger.log(
        'Successfully received response from OpenAI',
        'OpenAiProxyService',
      );

      return completion as OpenAiResponseDto;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to proxy request to OpenAI: ${errorMessage}`,
        errorStack,
        'OpenAiProxyService',
      );

      throw new ExternalServiceException(
        `OpenAI API request failed: ${errorMessage}`,
        'OpenAI',
      );
    }
  }
}
