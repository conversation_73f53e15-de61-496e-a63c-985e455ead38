import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { CustomLoggerService } from '../common/logger/logger.service';
import { OpenAiRequestDto } from './dto/openai-request.dto';
import { OpenAiResponseDto } from './dto/openai-response.dto';

@Injectable()
export class OpenAiProxyService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {}

  async proxyToOpenAI(payload: OpenAiRequestDto): Promise<OpenAiResponseDto> {
    const openaiConfig = this.configService.get('openai');

    this.logger.log('Proxying request to OpenAI', 'OpenAiProxyService');

    try {
      const res = await firstValueFrom(
        this.httpService.post(
          `${openaiConfig.baseUrl}/chat/completions`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${openaiConfig.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        'Successfully received response from OpenAI',
        'OpenAiProxyService',
      );
      return res.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to proxy request to OpenAI: ${errorMessage}`,
        errorStack,
        'OpenAiProxyService',
      );
      throw error;
    }
  }
}
