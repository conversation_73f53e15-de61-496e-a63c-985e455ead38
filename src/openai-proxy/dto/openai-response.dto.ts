import { ApiProperty } from '@nestjs/swagger';

export class ChoiceDto {
  @ApiProperty({ description: 'The index of the choice' })
  index: number;

  @ApiProperty({
    description: 'The message generated by the model',
    example: {
      role: 'assistant',
      content: 'Hello! How can I help you today?',
    },
  })
  message: {
    role: string;
    content: string;
  };

  @ApiProperty({
    description: 'The reason the model stopped generating tokens',
    example: 'stop',
  })
  finish_reason: string;
}

export class UsageDto {
  @ApiProperty({ description: 'Number of tokens in the prompt' })
  prompt_tokens: number;

  @ApiProperty({ description: 'Number of tokens in the completion' })
  completion_tokens: number;

  @ApiProperty({ description: 'Total number of tokens used' })
  total_tokens: number;
}

export class OpenAiResponseDto {
  @ApiProperty({ description: 'Unique identifier for the completion' })
  id: string;

  @ApiProperty({ description: 'Object type', example: 'chat.completion' })
  object: string;

  @ApiProperty({
    description: 'Unix timestamp of when the completion was created',
  })
  created: number;

  @ApiProperty({ description: 'Model used for the completion' })
  model: string;

  @ApiProperty({ description: 'List of completion choices', type: [ChoiceDto] })
  choices: ChoiceDto[];

  @ApiProperty({ description: 'Usage statistics for the completion request' })
  usage: UsageDto;
}
