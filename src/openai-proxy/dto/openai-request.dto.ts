import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>ber,
  IsBoolean,
  ValidateNested,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MessageDto {
  @ApiProperty({
    description: 'The role of the message author',
    example: 'user',
    enum: ['system', 'user', 'assistant'],
  })
  @IsString()
  role: string;

  @ApiProperty({
    description: 'The content of the message',
    example: 'Hello, how are you?',
  })
  @IsString()
  content: string;
}

export class OpenAiRequestDto {
  @ApiProperty({
    description: 'ID of the model to use',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  model: string;

  @ApiProperty({
    description: 'A list of messages comprising the conversation so far',
    type: [MessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  messages: MessageDto[];

  @ApiPropertyOptional({
    description: 'What sampling temperature to use, between 0 and 2',
    minimum: 0,
    maximum: 2,
    example: 0.7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({
    description: 'The maximum number of tokens to generate',
    minimum: 1,
    maximum: 4096,
    example: 150,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4096)
  max_tokens?: number;

  @ApiPropertyOptional({
    description: 'Nucleus sampling parameter',
    minimum: -2,
    maximum: 2,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  top_p?: number;

  @ApiPropertyOptional({
    description: 'Frequency penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  frequency_penalty?: number;

  @ApiPropertyOptional({
    description: 'Presence penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  presence_penalty?: number;

  @ApiPropertyOptional({
    description:
      'Up to 4 sequences where the API will stop generating further tokens',
    type: [String],
    example: ['\n'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  stop?: string[];

  @ApiPropertyOptional({
    description: 'Whether to stream back partial progress',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;
}
