import { Controller, Get } from '@nestjs/common';
import {
  HealthCheckService,
  HttpHealthIndicator,
  HealthCheck,
  MemoryHealthIndicator,
  DiskHealthIndicator,
  HealthIndicatorResult,
} from '@nestjs/terminus';
import { ConfigService } from '@nestjs/config';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheckResponse } from 'src/common/types';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly http: HttpHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly configService: ConfigService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Health check',
    description: 'Check the overall health of the application',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is healthy',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is unhealthy',
  })
  @HealthCheck()
  check() {
    return this.health.check([
      // Check if OpenAI API is reachable
      () =>
        this.http.pingCheck(
          'openai-api',
          this.configService.get<string>('openai.baseUrl') ??
            'https://api.openai.com/v1',
        ),
      // Check memory usage (should not exceed 150MB)
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      // Check RSS memory usage (should not exceed 150MB)
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      // Check disk storage (should have at least 250MB free)
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.9,
        }),
    ]);
  }

  @Get('liveness')
  @ApiOperation({
    summary: 'Liveness probe',
    description: 'Simple liveness check for Kubernetes/Docker',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
  })
  getLiveness(): { status: string; timestamp: string } {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('readiness')
  @ApiOperation({
    summary: 'Readiness probe',
    description: 'Check if application is ready to serve requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is ready',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not ready',
  })
  @HealthCheck()
  getReadiness(): Promise<HealthCheckResponse> {
    return this.health.check([
      // Check if OpenAI API is reachable for readiness
      (): Promise<HealthIndicatorResult<'openai-api'>> =>
        this.http.pingCheck(
          'openai-api',
          this.configService.get<string>('openai.baseUrl') ??
            'https://api.openai.com/v1',
        ),
    ]);
  }
}
